import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { FormTemplate, FieldConfig, FormBuilderState } from '@/types/form'
import { nanoid } from 'nanoid'

interface FormBuilderStore extends FormBuilderState {
  // Actions
  setCurrentForm: (form: Partial<FormTemplate>) => void
  updateFormField: (field: string, value: any) => void
  addField: (fieldType: FieldConfig['type'], position?: number) => void
  updateField: (fieldId: string, updates: Partial<FieldConfig>) => void
  removeField: (fieldId: string) => void
  reorderFields: (fromIndex: number, toIndex: number) => void
  selectField: (fieldId: string | null) => void
  setDraggedField: (field: FieldConfig | null) => void
  togglePreviewMode: () => void
  resetForm: () => void
  setDirty: (dirty: boolean) => void
  duplicateField: (fieldId: string) => void
}

const createDefaultField = (type: FieldConfig['type'], order: number): FieldConfig => {
  const baseField = {
    id: nanoid(),
    type,
    label: `${type.charAt(0).toUpperCase() + type.slice(1)} Field`,
    order,
    validation: {
      required: false,
    },
  }

  switch (type) {
    case 'text':
      return {
        ...baseField,
        placeholder: 'Enter text...',
      }
    case 'textarea':
      return {
        ...baseField,
        placeholder: 'Enter your message...',
      }
    case 'number':
      return {
        ...baseField,
        placeholder: 'Enter a number...',
        validation: {
          ...baseField.validation,
          min: 0,
        },
      }
    case 'date':
      return {
        ...baseField,
        placeholder: 'Select a date...',
      }
    case 'dropdown':
      return {
        ...baseField,
        placeholder: 'Select an option...',
        options: [
          { label: 'Option 1', value: 'option1' },
          { label: 'Option 2', value: 'option2' },
        ],
      }
    case 'radio':
      return {
        ...baseField,
        options: [
          { label: 'Option 1', value: 'option1' },
          { label: 'Option 2', value: 'option2' },
        ],
      }
    case 'checkbox':
      return {
        ...baseField,
        options: [
          { label: 'Option 1', value: 'option1' },
          { label: 'Option 2', value: 'option2' },
        ],
      }
    case 'file':
      return {
        ...baseField,
        placeholder: 'Choose file...',
      }
    default:
      return baseField
  }
}

export const useFormBuilderStore = create<FormBuilderStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      currentForm: {
        title: 'Untitled Form',
        description: '',
        fields: [],
        settings: {
          isActive: true,
          allowDrafts: true,
          requireAuth: true,
        },
      },
      selectedField: null,
      draggedField: null,
      isPreviewMode: false,
      isDirty: false,

      // Actions
      setCurrentForm: (form) =>
        set((state) => ({
          currentForm: { ...state.currentForm, ...form },
          isDirty: true,
        })),

      updateFormField: (field, value) =>
        set((state) => ({
          currentForm: {
            ...state.currentForm,
            [field]: value,
          },
          isDirty: true,
        })),

      addField: (fieldType, position) =>
        set((state) => {
          const fields = state.currentForm.fields || []
          const order = position !== undefined ? position : fields.length
          const newField = createDefaultField(fieldType, order)
          
          // Adjust order of existing fields if inserting in the middle
          const updatedFields = fields.map((field) =>
            field.order >= order ? { ...field, order: field.order + 1 } : field
          )
          
          updatedFields.push(newField)
          updatedFields.sort((a, b) => a.order - b.order)

          return {
            currentForm: {
              ...state.currentForm,
              fields: updatedFields,
            },
            selectedField: newField.id,
            isDirty: true,
          }
        }),

      updateField: (fieldId, updates) =>
        set((state) => {
          const fields = state.currentForm.fields || []
          const updatedFields = fields.map((field) =>
            field.id === fieldId ? { ...field, ...updates } : field
          )

          return {
            currentForm: {
              ...state.currentForm,
              fields: updatedFields,
            },
            isDirty: true,
          }
        }),

      removeField: (fieldId) =>
        set((state) => {
          const fields = state.currentForm.fields || []
          const updatedFields = fields.filter((field) => field.id !== fieldId)

          return {
            currentForm: {
              ...state.currentForm,
              fields: updatedFields,
            },
            selectedField: state.selectedField === fieldId ? null : state.selectedField,
            isDirty: true,
          }
        }),

      reorderFields: (fromIndex, toIndex) =>
        set((state) => {
          const fields = [...(state.currentForm.fields || [])]
          const [movedField] = fields.splice(fromIndex, 1)
          fields.splice(toIndex, 0, movedField)

          // Update order property
          const updatedFields = fields.map((field, index) => ({
            ...field,
            order: index,
          }))

          return {
            currentForm: {
              ...state.currentForm,
              fields: updatedFields,
            },
            isDirty: true,
          }
        }),

      selectField: (fieldId) =>
        set(() => ({
          selectedField: fieldId,
        })),

      setDraggedField: (field) =>
        set(() => ({
          draggedField: field,
        })),

      togglePreviewMode: () =>
        set((state) => ({
          isPreviewMode: !state.isPreviewMode,
          selectedField: null,
        })),

      resetForm: () =>
        set(() => ({
          currentForm: {
            title: 'Untitled Form',
            description: '',
            fields: [],
            settings: {
              isActive: true,
              allowDrafts: true,
              requireAuth: true,
            },
          },
          selectedField: null,
          draggedField: null,
          isPreviewMode: false,
          isDirty: false,
        })),

      setDirty: (dirty) =>
        set(() => ({
          isDirty: dirty,
        })),

      duplicateField: (fieldId) =>
        set((state) => {
          const fields = state.currentForm.fields || []
          const fieldToDuplicate = fields.find((field) => field.id === fieldId)
          
          if (!fieldToDuplicate) return state

          const duplicatedField = {
            ...fieldToDuplicate,
            id: nanoid(),
            label: `${fieldToDuplicate.label} (Copy)`,
            order: fieldToDuplicate.order + 1,
          }

          // Adjust order of fields that come after the duplicated field
          const updatedFields = fields.map((field) =>
            field.order > fieldToDuplicate.order
              ? { ...field, order: field.order + 1 }
              : field
          )

          updatedFields.push(duplicatedField)
          updatedFields.sort((a, b) => a.order - b.order)

          return {
            currentForm: {
              ...state.currentForm,
              fields: updatedFields,
            },
            selectedField: duplicatedField.id,
            isDirty: true,
          }
        }),
    }),
    {
      name: 'form-builder-store',
    }
  )
)
