{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/from-creator/dynamic-form-creator/src/components/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { FormInput, Users, Settings, Home } from 'lucide-react'\n\nconst Navigation = () => {\n  const pathname = usePathname()\n\n  const navItems = [\n    {\n      href: '/',\n      label: 'Dashboard',\n      icon: Home,\n    },\n    {\n      href: '/forms',\n      label: 'Forms',\n      icon: FormInput,\n    },\n    {\n      href: '/responses',\n      label: 'Responses',\n      icon: Users,\n    },\n    {\n      href: '/settings',\n      label: 'Settings',\n      icon: Settings,\n    },\n  ]\n\n  return (\n    <div className=\"navbar bg-base-100 shadow-lg\">\n      <div className=\"navbar-start\">\n        <Link href=\"/\" className=\"btn btn-ghost text-xl\">\n          Dynamic Form Creator\n        </Link>\n      </div>\n      \n      <div className=\"navbar-center hidden lg:flex\">\n        <ul className=\"menu menu-horizontal px-1\">\n          {navItems.map((item) => {\n            const Icon = item.icon\n            const isActive = pathname === item.href\n            \n            return (\n              <li key={item.href}>\n                <Link\n                  href={item.href}\n                  className={`flex items-center gap-2 ${\n                    isActive ? 'active' : ''\n                  }`}\n                >\n                  <Icon size={18} />\n                  {item.label}\n                </Link>\n              </li>\n            )\n          })}\n        </ul>\n      </div>\n\n      <div className=\"navbar-end\">\n        <div className=\"dropdown dropdown-end\">\n          <div tabIndex={0} role=\"button\" className=\"btn btn-ghost btn-circle avatar\">\n            <div className=\"w-10 rounded-full bg-primary text-primary-content flex items-center justify-center\">\n              <span className=\"text-sm font-medium\">U</span>\n            </div>\n          </div>\n          <ul\n            tabIndex={0}\n            className=\"menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-52 p-2 shadow\"\n          >\n            <li>\n              <a className=\"justify-between\">\n                Profile\n                <span className=\"badge\">New</span>\n              </a>\n            </li>\n            <li><a>Settings</a></li>\n            <li><a>Logout</a></li>\n          </ul>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      <div className=\"navbar-end lg:hidden\">\n        <div className=\"dropdown dropdown-end\">\n          <div tabIndex={0} role=\"button\" className=\"btn btn-ghost\">\n            <svg\n              className=\"w-5 h-5\"\n              aria-hidden=\"true\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 17 14\"\n            >\n              <path\n                stroke=\"currentColor\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth=\"2\"\n                d=\"M1 1h15M1 7h15M1 13h15\"\n              />\n            </svg>\n          </div>\n          <ul\n            tabIndex={0}\n            className=\"menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-52 p-2 shadow\"\n          >\n            {navItems.map((item) => {\n              const Icon = item.icon\n              const isActive = pathname === item.href\n              \n              return (\n                <li key={item.href}>\n                  <Link\n                    href={item.href}\n                    className={`flex items-center gap-2 ${\n                      isActive ? 'active' : ''\n                    }`}\n                  >\n                    <Icon size={18} />\n                    {item.label}\n                  </Link>\n                </li>\n              )\n            })}\n          </ul>\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport default Navigation\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,aAAa;IACjB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,MAAM,mMAAA,CAAA,OAAI;QACZ;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM,wNAAA,CAAA,YAAS;QACjB;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM,oMAAA,CAAA,QAAK;QACb;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM,0MAAA,CAAA,WAAQ;QAChB;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;oBAAI,WAAU;8BAAwB;;;;;;;;;;;0BAKnD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BACX,SAAS,GAAG,CAAC,CAAC;wBACb,MAAM,OAAO,KAAK,IAAI;wBACtB,MAAM,WAAW,aAAa,KAAK,IAAI;wBAEvC,qBACE,8OAAC;sCACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,wBAAwB,EAClC,WAAW,WAAW,IACtB;;kDAEF,8OAAC;wCAAK,MAAM;;;;;;oCACX,KAAK,KAAK;;;;;;;2BARN,KAAK,IAAI;;;;;oBAYtB;;;;;;;;;;;0BAIJ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,UAAU;4BAAG,MAAK;4BAAS,WAAU;sCACxC,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAsB;;;;;;;;;;;;;;;;sCAG1C,8OAAC;4BACC,UAAU;4BACV,WAAU;;8CAEV,8OAAC;8CACC,cAAA,8OAAC;wCAAE,WAAU;;4CAAkB;0DAE7B,8OAAC;gDAAK,WAAU;0DAAQ;;;;;;;;;;;;;;;;;8CAG5B,8OAAC;8CAAG,cAAA,8OAAC;kDAAE;;;;;;;;;;;8CACP,8OAAC;8CAAG,cAAA,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,UAAU;4BAAG,MAAK;4BAAS,WAAU;sCACxC,cAAA,8OAAC;gCACC,WAAU;gCACV,eAAY;gCACZ,OAAM;gCACN,MAAK;gCACL,SAAQ;0CAER,cAAA,8OAAC;oCACC,QAAO;oCACP,eAAc;oCACd,gBAAe;oCACf,aAAY;oCACZ,GAAE;;;;;;;;;;;;;;;;sCAIR,8OAAC;4BACC,UAAU;4BACV,WAAU;sCAET,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;gCAEvC,qBACE,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,wBAAwB,EAClC,WAAW,WAAW,IACtB;;0DAEF,8OAAC;gDAAK,MAAM;;;;;;4CACX,KAAK,KAAK;;;;;;;mCARN,KAAK,IAAI;;;;;4BAYtB;;;;;;;;;;;;;;;;;;;;;;;AAMZ;uCAEe", "debugId": null}}]}