[{"name": "hot-reloader", "duration": 4130, "timestamp": 592104371204, "id": 3, "tags": {"version": "15.3.4"}, "startTime": 1751383366430, "traceId": "274dce8021bbdf20"}, {"name": "setup-dev-bundler", "duration": 5173625, "timestamp": 592099539041, "id": 2, "parentId": 1, "tags": {}, "startTime": 1751383361593, "traceId": "274dce8021bbdf20"}, {"name": "run-instrumentation-hook", "duration": 12, "timestamp": 592104746030, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751383366800, "traceId": "274dce8021bbdf20"}, {"name": "start-dev-server", "duration": 5504313, "timestamp": 592099248833, "id": 1, "tags": {"cpus": "10", "platform": "darwin", "memory.freeMem": "198017024", "memory.totalMem": "17179869184", "memory.heapSizeLimit": "8640266240", "memory.rss": "283410432", "memory.heapTotal": "99090432", "memory.heapUsed": "73060464"}, "startTime": 1751383361303, "traceId": "274dce8021bbdf20"}, {"name": "compile-path", "duration": 80931514, "timestamp": 592118548093, "id": 7, "tags": {"trigger": "/"}, "startTime": 1751383380602, "traceId": "274dce8021bbdf20"}, {"name": "ensure-page", "duration": 80932342, "timestamp": 592118547681, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1751383380602, "traceId": "274dce8021bbdf20"}]