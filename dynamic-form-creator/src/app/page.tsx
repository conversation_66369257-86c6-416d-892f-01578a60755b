import Navigation from '@/components/Navigation'
import Link from 'next/link'
import { Plus, FormInput, Users, BarChart3 } from 'lucide-react'

export default function Home() {
  return (
    <div className="min-h-screen bg-base-100">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="hero bg-gradient-to-r from-primary to-secondary text-primary-content rounded-lg mb-8">
          <div className="hero-content text-center">
            <div className="max-w-md">
              <h1 className="mb-5 text-5xl font-bold">Dynamic Form Creator</h1>
              <p className="mb-5">
                Create, manage, and collect responses from custom forms for your organization.
                Build powerful forms with drag-and-drop simplicity.
              </p>
              <Link href="/forms/create" className="btn btn-accent">
                <Plus size={20} />
                Create Your First Form
              </Link>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="stats shadow w-full mb-8">
          <div className="stat">
            <div className="stat-figure text-primary">
              <FormInput size={32} />
            </div>
            <div className="stat-title">Total Forms</div>
            <div className="stat-value text-primary">0</div>
            <div className="stat-desc">Forms created</div>
          </div>

          <div className="stat">
            <div className="stat-figure text-secondary">
              <Users size={32} />
            </div>
            <div className="stat-title">Total Responses</div>
            <div className="stat-value text-secondary">0</div>
            <div className="stat-desc">Responses collected</div>
          </div>

          <div className="stat">
            <div className="stat-figure text-accent">
              <BarChart3 size={32} />
            </div>
            <div className="stat-title">Active Forms</div>
            <div className="stat-value text-accent">0</div>
            <div className="stat-desc">Currently active</div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div className="card bg-base-200 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">
                <FormInput size={24} />
                Create New Form
              </h2>
              <p>Build a custom form with our intuitive drag-and-drop builder.</p>
              <div className="card-actions justify-end">
                <Link href="/forms/create" className="btn btn-primary">
                  Get Started
                </Link>
              </div>
            </div>
          </div>

          <div className="card bg-base-200 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">
                <Users size={24} />
                View Responses
              </h2>
              <p>Review and analyze responses from your published forms.</p>
              <div className="card-actions justify-end">
                <Link href="/responses" className="btn btn-secondary">
                  View All
                </Link>
              </div>
            </div>
          </div>

          <div className="card bg-base-200 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">
                <BarChart3 size={24} />
                Analytics
              </h2>
              <p>Get insights into form performance and user engagement.</p>
              <div className="card-actions justify-end">
                <Link href="/analytics" className="btn btn-accent">
                  View Analytics
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="card bg-base-200 shadow-xl">
          <div className="card-body">
            <h2 className="card-title mb-4">Recent Activity</h2>
            <div className="text-center py-8 text-base-content/60">
              <FormInput size={48} className="mx-auto mb-4 opacity-50" />
              <p>No recent activity. Create your first form to get started!</p>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
